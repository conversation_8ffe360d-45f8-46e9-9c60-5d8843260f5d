#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vehicle Management Interface
A French vehicle management system with bright yellow background
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import os

class VehicleManagementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Gestion des Véhicules")
        self.root.geometry("800x700")
        self.root.configure(bg='#FFFF00')  # Bright yellow background

        # Vehicle data
        self.vehicle_data = {
            'nom_prenom': 'XXXXXXX XXXXXXXX XXXXXXX',
            'vehicule': 'RENAULT CLIO XXXXXXX',
            'date_mise_fourriere': '22/01/2019',
            'date_retrait': '24/01/2019',
            'nombre_jours': 3,
            'taux': 20.00,
            'montant_payer': 60.00,
            'numero_quittance': '22808',
            'somme_total': 113570.00
        }

        # Vehicle types with selection state
        self.vehicle_types = {
            'bicyclette': False,
            'moto': False,
            'voiture': True,  # Selected by default
            'camionnette': False,
            'camion': False,
            'grand_camion': False,
            'materiel_ammort': False,
            'materiel_non_ammort': False
        }

        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface"""
        # Top button bar
        self.create_top_buttons()

        # Vehicle information section
        self.create_vehicle_info_section()

        # Vehicle type selection grid
        self.create_vehicle_type_grid()

        # Financial summary section
        self.create_financial_section()

        # Action buttons
        self.create_action_buttons()

        # Bottom total
        self.create_bottom_total()

    def create_top_buttons(self):
        """Create the top button bar"""
        button_frame = tk.Frame(self.root, bg='#FFFF00')
        button_frame.pack(pady=10, padx=10, fill='x')

        buttons = ['Ajouter', 'Sauvegarder', 'Rechercher', 'Supprimer', 'Actualiser']

        for i, btn_text in enumerate(buttons):
            btn = tk.Button(
                button_frame,
                text=btn_text,
                width=12,
                height=2,
                bg='lightgray',
                relief='raised',
                command=lambda t=btn_text: self.button_action(t)
            )
            btn.grid(row=0, column=i, padx=5)

    def create_vehicle_info_section(self):
        """Create vehicle information input section"""
        info_frame = tk.Frame(self.root, bg='#FFFF00')
        info_frame.pack(pady=10, padx=20, fill='x')

        # Name and surname
        tk.Label(info_frame, text="NOM ET PRENOM:", bg='#FFFF00', font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w')
        self.nom_entry = tk.Entry(info_frame, width=40, bg='lightgray')
        self.nom_entry.insert(0, self.vehicle_data['nom_prenom'])
        self.nom_entry.grid(row=0, column=1, padx=5, sticky='w')

        # Vehicle name
        tk.Label(info_frame, text="nom de véhicule ou matricule:", bg='#FFFF00', font=('Arial', 10)).grid(row=1, column=0, sticky='w')
        self.vehicule_entry = tk.Entry(info_frame, width=40, bg='lightgray')
        self.vehicule_entry.insert(0, self.vehicle_data['vehicule'])
        self.vehicule_entry.grid(row=1, column=1, padx=5, sticky='w')

        # Dates
        tk.Label(info_frame, text="DATE DE MISE EN FOURRIERE:", bg='#FFFF00', font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='w')
        self.date_fourriere_entry = tk.Entry(info_frame, width=15, bg='lightgray')
        self.date_fourriere_entry.insert(0, self.vehicle_data['date_mise_fourriere'])
        self.date_fourriere_entry.grid(row=2, column=1, padx=5, sticky='w')

        tk.Label(info_frame, text="DATE DE RETRAIT:", bg='#FFFF00', font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky='w')
        self.date_retrait_entry = tk.Entry(info_frame, width=15, bg='lightgray')
        self.date_retrait_entry.insert(0, self.vehicle_data['date_retrait'])
        self.date_retrait_entry.grid(row=3, column=1, padx=5, sticky='w')

    def create_vehicle_type_grid(self):
        """Create vehicle type selection grid with icons"""
        grid_frame = tk.Frame(self.root, bg='#FFFF00', relief='solid', bd=2)
        grid_frame.pack(pady=10, padx=20, fill='x')

        # Vehicle type checkboxes and labels
        vehicle_info = [
            ('bicyclette', '🚲', 0, 0),
            ('camion', '🚛', 0, 1),
            ('moto', '🏍️', 1, 0),
            ('grand camion', '🚚', 1, 1),
            ('voiture', '🚗', 2, 0),
            ('matériel ammort', '♻️', 2, 1),
            ('camionnette', '🚐', 3, 0),
            ('matériel non ammort', '📦', 3, 1)
        ]

        self.vehicle_vars = {}

        for vehicle_type, icon, row, col in vehicle_info:
            frame = tk.Frame(grid_frame, bg='white', relief='solid', bd=1)
            frame.grid(row=row, column=col, padx=2, pady=2, sticky='ew', ipadx=10, ipady=5)

            # Checkbox
            var = tk.BooleanVar()
            if vehicle_type == 'voiture':
                var.set(True)  # Voiture is selected by default

            checkbox = tk.Checkbutton(frame, variable=var, bg='white')
            checkbox.pack(side='left')

            # Icon and label
            label = tk.Label(frame, text=f"{icon} {vehicle_type}", bg='white', font=('Arial', 10))
            label.pack(side='left', padx=5)

            self.vehicle_vars[vehicle_type] = var

        # Configure grid weights
        grid_frame.grid_columnconfigure(0, weight=1)
        grid_frame.grid_columnconfigure(1, weight=1)

        # Selected vehicle type display
        selected_frame = tk.Frame(self.root, bg='#FFFF00')
        selected_frame.pack(pady=5)

        tk.Label(selected_frame, text="VOITURE", bg='lightgray', font=('Arial', 12, 'bold'),
                relief='solid', bd=1, padx=20, pady=5).pack()

    def create_financial_section(self):
        """Create financial summary section"""
        finance_frame = tk.Frame(self.root, bg='#FFFF00')
        finance_frame.pack(pady=10, padx=20, fill='x')

        # Financial data labels and entries
        financial_data = [
            ("NOMBRE TOTAL DE JOURS:", str(self.vehicle_data['nombre_jours'])),
            ("TAUX:", f"{self.vehicle_data['taux']:.2f} DH"),
            ("MONTANT A PAYER:", f"{self.vehicle_data['montant_payer']:.2f} DH"),
            ("NUMERO DE LA QUITTANCE:", str(self.vehicle_data['numero_quittance']))
        ]

        for i, (label_text, value) in enumerate(financial_data):
            tk.Label(finance_frame, text=label_text, bg='#FFFF00',
                    font=('Arial', 10, 'bold')).grid(row=i, column=0, sticky='w', pady=2)

            entry = tk.Entry(finance_frame, width=20, bg='lightgray')
            entry.insert(0, value)
            entry.grid(row=i, column=1, padx=10, sticky='w', pady=2)

        # Navigation buttons
        nav_frame = tk.Frame(finance_frame, bg='#FFFF00')
        nav_frame.grid(row=0, column=2, rowspan=4, padx=20)

        tk.Button(nav_frame, text="◀", width=3, command=self.prev_record).pack(side='left', padx=2)
        tk.Button(nav_frame, text="▶", width=3, command=self.next_record).pack(side='left', padx=2)

    def create_action_buttons(self):
        """Create action buttons for printing and listing"""
        action_frame = tk.Frame(self.root, bg='#FFFF00')
        action_frame.pack(pady=10)

        tk.Button(action_frame, text="imprimer bon", width=15, height=2,
                 bg='lightgray', command=self.print_receipt).pack(side='left', padx=5)

        tk.Button(action_frame, text="imprimer bon 3\nCOPIES", width=15, height=2,
                 bg='lightgray', command=self.print_copies).pack(side='left', padx=5)

        tk.Button(action_frame, text="LISTE", width=15, height=2,
                 bg='lightgray', command=self.show_list).pack(side='left', padx=5)

    def create_bottom_total(self):
        """Create bottom total display"""
        total_frame = tk.Frame(self.root, bg='#FFFF00')
        total_frame.pack(pady=20, side='bottom')

        tk.Label(total_frame, text="somme total", bg='#FFFF00',
                font=('Arial', 12, 'bold')).pack(side='left', padx=10)

        total_label = tk.Label(total_frame, text=f"{self.vehicle_data['somme_total']:.2f} Dh",
                              bg='white', font=('Arial', 16, 'bold'),
                              relief='solid', bd=2, padx=20, pady=10)
        total_label.pack(side='left')

    # Event handlers
    def button_action(self, action):
        """Handle top button actions"""
        messagebox.showinfo("Action", f"Action '{action}' sélectionnée")

    def prev_record(self):
        """Navigate to previous record"""
        messagebox.showinfo("Navigation", "Enregistrement précédent")

    def next_record(self):
        """Navigate to next record"""
        messagebox.showinfo("Navigation", "Enregistrement suivant")

    def print_receipt(self):
        """Print receipt"""
        messagebox.showinfo("Impression", "Impression du bon en cours...")

    def print_copies(self):
        """Print 3 copies"""
        messagebox.showinfo("Impression", "Impression de 3 copies en cours...")

    def show_list(self):
        """Show list"""
        messagebox.showinfo("Liste", "Affichage de la liste")

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = VehicleManagementApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
