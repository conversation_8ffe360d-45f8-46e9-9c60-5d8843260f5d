#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vehicle Selector Component - Advanced Vehicle Selection Interface
Provides detailed filtering and selection for cars and motorcycles
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional, Dict, Any
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from models.vehicle_catalog import vehicle_catalog, VehicleCategory, VehicleModel


class VehicleSelector(tk.Frame):
    """
    Advanced Vehicle Selector Component

    Features:
    - Cascading dropdowns (Type -> Brand -> Model)
    - Category filtering
    - Detailed model information
    - Search functionality
    - Professional styling
    """

    def __init__(self, parent, vehicle_type: str = "voiture",
                 on_selection_change: Optional[Callable] = None,
                 theme_config: Optional[Any] = None):
        super().__init__(parent)

        self.vehicle_type = vehicle_type
        self.on_selection_change = on_selection_change
        self.theme_config = theme_config or self._default_theme()

        # Selection variables
        self.selected_brand = tk.StringVar()
        self.selected_model = tk.StringVar()
        self.selected_category = tk.StringVar()
        self.search_query = tk.StringVar()

        # Bind events
        self.selected_brand.trace('w', self._on_brand_change)
        self.selected_model.trace('w', self._on_model_change)
        self.selected_category.trace('w', self._on_category_change)
        self.search_query.trace('w', self._on_search_change)

        # UI components
        self.brand_combo = None
        self.model_combo = None
        self.category_combo = None
        self.search_entry = None
        self.info_text = None

        self._create_ui()
        self._populate_initial_data()

    def _default_theme(self):
        """Default theme configuration"""
        class Theme:
            background_color = "#FFFF00"
            font_family = "Arial"
            font_size = 10
        return Theme()

    def _create_ui(self):
        """Create the vehicle selector interface"""
        self.configure(bg=self.theme_config.background_color)

        # Main container
        main_frame = tk.Frame(self, bg=self.theme_config.background_color)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_text = "Sélection Automobile" if self.vehicle_type == "voiture" else "Sélection Moto"
        title_label = tk.Label(
            main_frame,
            text=title_text,
            bg=self.theme_config.background_color,
            font=(self.theme_config.font_family, self.theme_config.font_size + 2, 'bold')
        )
        title_label.pack(pady=(0, 10))

        # Search section
        self._create_search_section(main_frame)

        # Selection section
        self._create_selection_section(main_frame)

        # Information section
        self._create_info_section(main_frame)

    def _create_search_section(self, parent):
        """Create search functionality"""
        search_frame = tk.LabelFrame(
            parent,
            text="🔍 Recherche Rapide",
            bg=self.theme_config.background_color,
            font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')
        )
        search_frame.pack(fill=tk.X, pady=(0, 10))

        search_container = tk.Frame(search_frame, bg=self.theme_config.background_color)
        search_container.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            search_container,
            text="Rechercher:",
            bg=self.theme_config.background_color,
            font=(self.theme_config.font_family, self.theme_config.font_size)
        ).pack(side=tk.LEFT)

        self.search_entry = tk.Entry(
            search_container,
            textvariable=self.search_query,
            width=30,
            font=(self.theme_config.font_family, self.theme_config.font_size)
        )
        self.search_entry.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)

        # Clear button
        clear_btn = tk.Button(
            search_container,
            text="✕",
            command=self._clear_search,
            width=3,
            font=(self.theme_config.font_family, self.theme_config.font_size)
        )
        clear_btn.pack(side=tk.RIGHT, padx=(5, 0))

    def _create_selection_section(self, parent):
        """Create cascading selection dropdowns"""
        selection_frame = tk.LabelFrame(
            parent,
            text="🚗 Sélection Détaillée" if self.vehicle_type == "voiture" else "🏍️ Sélection Détaillée",
            bg=self.theme_config.background_color,
            font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')
        )
        selection_frame.pack(fill=tk.X, pady=(0, 10))

        # Grid layout for selections
        grid_frame = tk.Frame(selection_frame, bg=self.theme_config.background_color)
        grid_frame.pack(fill=tk.X, padx=10, pady=10)

        # Category selection with add button
        tk.Label(
            grid_frame,
            text="Catégorie:",
            bg=self.theme_config.background_color,
            font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')
        ).grid(row=0, column=0, sticky='w', pady=5)

        # Category frame to hold combobox and add button
        category_frame = tk.Frame(grid_frame, bg=self.theme_config.background_color)
        category_frame.grid(row=0, column=1, padx=(10, 0), pady=5, sticky='ew')

        self.category_combo = ttk.Combobox(
            category_frame,
            textvariable=self.selected_category,
            state="readonly",
            width=18,
            font=(self.theme_config.font_family, self.theme_config.font_size)
        )
        self.category_combo.pack(side='left', fill='x', expand=True)

        # Add category button with + symbol
        self.add_category_btn = tk.Button(
            category_frame,
            text="➕",
            width=3,
            height=1,
            bg='lightgreen',
            font=(self.theme_config.font_family, self.theme_config.font_size + 2, 'bold'),
            command=self._add_custom_category,
            relief='raised'
        )
        self.add_category_btn.pack(side='right', padx=(5, 0))

        # Brand selection
        tk.Label(
            grid_frame,
            text="Marque:",
            bg=self.theme_config.background_color,
            font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')
        ).grid(row=1, column=0, sticky='w', pady=5)

        self.brand_combo = ttk.Combobox(
            grid_frame,
            textvariable=self.selected_brand,
            state="readonly",
            width=20,
            font=(self.theme_config.font_family, self.theme_config.font_size)
        )
        self.brand_combo.grid(row=1, column=1, padx=(10, 0), pady=5, sticky='ew')

        # Model selection
        tk.Label(
            grid_frame,
            text="Modèle:",
            bg=self.theme_config.background_color,
            font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')
        ).grid(row=2, column=0, sticky='w', pady=5)

        self.model_combo = ttk.Combobox(
            grid_frame,
            textvariable=self.selected_model,
            state="readonly",
            width=20,
            font=(self.theme_config.font_family, self.theme_config.font_size)
        )
        self.model_combo.grid(row=2, column=1, padx=(10, 0), pady=5, sticky='ew')

        # Configure grid weights
        grid_frame.grid_columnconfigure(1, weight=1)

        # Action buttons
        button_frame = tk.Frame(selection_frame, bg=self.theme_config.background_color)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        tk.Button(
            button_frame,
            text="🔄 Réinitialiser",
            command=self._reset_selection,
            font=(self.theme_config.font_family, self.theme_config.font_size)
        ).pack(side=tk.LEFT)

        tk.Button(
            button_frame,
            text="ℹ️ Détails",
            command=self._show_model_details,
            font=(self.theme_config.font_family, self.theme_config.font_size)
        ).pack(side=tk.LEFT, padx=(10, 0))

    def _create_info_section(self, parent):
        """Create information display section"""
        info_frame = tk.LabelFrame(
            parent,
            text="📋 Informations du Véhicule",
            bg=self.theme_config.background_color,
            font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')
        )
        info_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollable text widget
        text_frame = tk.Frame(info_frame, bg=self.theme_config.background_color)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.info_text = tk.Text(
            text_frame,
            height=6,
            wrap=tk.WORD,
            font=(self.theme_config.font_family, self.theme_config.font_size),
            bg='white',
            relief='solid',
            bd=1
        )
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.info_text.configure(yscrollcommand=scrollbar.set)

        # Initial info
        self._update_info_display("Sélectionnez un véhicule pour voir les détails...")

    def _populate_initial_data(self):
        """Populate initial dropdown data"""
        # Populate categories (including custom ones)
        categories = vehicle_catalog.get_all_categories_for_type(self.vehicle_type)
        category_values = ["Toutes les catégories"] + [cat.display_name for cat in categories]
        self.category_combo['values'] = category_values
        self.category_combo.set("Toutes les catégories")

        # Populate brands
        brands = vehicle_catalog.get_brands_for_type(self.vehicle_type)
        self.brand_combo['values'] = brands

        # Clear models initially
        self.model_combo['values'] = []

    def _on_category_change(self, *args):
        """Handle category selection change"""
        category_name = self.selected_category.get()

        if category_name == "Toutes les catégories":
            # Show all brands
            brands = vehicle_catalog.get_brands_for_type(self.vehicle_type)
            self.brand_combo['values'] = brands
        else:
            # Filter brands by category
            categories = vehicle_catalog.get_categories_for_type(self.vehicle_type)
            selected_category = next((cat for cat in categories if cat.display_name == category_name), None)

            if selected_category:
                filtered_models = vehicle_catalog.filter_models_by_category(self.vehicle_type, selected_category)
                brands = list(filtered_models.keys())
                self.brand_combo['values'] = brands

        # Reset brand and model selection
        self.selected_brand.set("")
        self.selected_model.set("")
        self.model_combo['values'] = []

    def _on_brand_change(self, *args):
        """Handle brand selection change"""
        brand = self.selected_brand.get()
        category_name = self.selected_category.get()

        if brand:
            if category_name == "Toutes les catégories":
                # Show all models for brand
                models = vehicle_catalog.get_models_for_brand(self.vehicle_type, brand)
            else:
                # Filter models by category
                categories = vehicle_catalog.get_categories_for_type(self.vehicle_type)
                selected_category = next((cat for cat in categories if cat.display_name == category_name), None)

                if selected_category:
                    filtered_models = vehicle_catalog.filter_models_by_category(self.vehicle_type, selected_category)
                    models = filtered_models.get(brand, [])
                else:
                    models = []

            model_names = [model.name for model in models]
            self.model_combo['values'] = model_names
        else:
            self.model_combo['values'] = []

        # Reset model selection
        self.selected_model.set("")

    def _on_model_change(self, *args):
        """Handle model selection change"""
        self._update_model_info()
        if self.on_selection_change:
            self.on_selection_change(self.get_selection())

    def _on_search_change(self, *args):
        """Handle search query change"""
        query = self.search_query.get().strip()

        if len(query) >= 2:  # Start searching after 2 characters
            results = vehicle_catalog.search_models(query, self.vehicle_type)

            if results:
                # Update info with search results
                info_text = f"🔍 Résultats de recherche pour '{query}':\n\n"
                for brand_type, models in results.items():
                    info_text += f"📁 {brand_type}:\n"
                    for model in models[:3]:  # Show first 3 results
                        info_text += f"  • {model.name} ({model.category.display_name})\n"
                    if len(models) > 3:
                        info_text += f"  ... et {len(models) - 3} autres\n"
                    info_text += "\n"

                self._update_info_display(info_text)
            else:
                self._update_info_display(f"❌ Aucun résultat trouvé pour '{query}'")
        elif not query:
            # Clear search, show current selection info
            self._update_model_info()

    def _update_model_info(self):
        """Update model information display"""
        brand = self.selected_brand.get()
        model_name = self.selected_model.get()

        if brand and model_name:
            model = vehicle_catalog.get_model_details(self.vehicle_type, brand, model_name)
            if model:
                info_text = f"🚗 {brand} {model.name}\n\n"
                info_text += f"📂 Catégorie: {model.category.display_name}\n"
                if model.engine_size:
                    info_text += f"🔧 Motorisation: {model.engine_size}\n"
                if model.fuel_type:
                    info_text += f"⛽ Carburant: {model.fuel_type}\n"
                if model.year_range:
                    info_text += f"📅 Années: {model.year_range}\n"
                if model.description:
                    info_text += f"\n📝 Description:\n{model.description}"

                self._update_info_display(info_text)
            else:
                self._update_info_display("❌ Informations non disponibles")
        else:
            self._update_info_display("Sélectionnez un véhicule pour voir les détails...")

    def _update_info_display(self, text: str):
        """Update the information display"""
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, text)

    def _clear_search(self):
        """Clear search query"""
        self.search_query.set("")

    def _reset_selection(self):
        """Reset all selections"""
        self.selected_category.set("Toutes les catégories")
        self.selected_brand.set("")
        self.selected_model.set("")
        self.search_query.set("")
        self._populate_initial_data()
        self._update_info_display("Sélection réinitialisée. Choisissez un véhicule...")

    def _show_model_details(self):
        """Show detailed model information in popup"""
        brand = self.selected_brand.get()
        model_name = self.selected_model.get()

        if not (brand and model_name):
            tk.messagebox.showwarning("Attention", "Veuillez sélectionner un modèle d'abord.")
            return

        model = vehicle_catalog.get_model_details(self.vehicle_type, brand, model_name)
        if model:
            detail_window = tk.Toplevel(self)
            detail_window.title(f"Détails - {brand} {model.name}")
            detail_window.geometry("400x300")
            detail_window.configure(bg=self.theme_config.background_color)

            # Center the window
            detail_window.update_idletasks()
            x = (detail_window.winfo_screenwidth() // 2) - (400 // 2)
            y = (detail_window.winfo_screenheight() // 2) - (300 // 2)
            detail_window.geometry(f'400x300+{x}+{y}')

            # Content
            content_frame = tk.Frame(detail_window, bg=self.theme_config.background_color)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # Title
            title_label = tk.Label(
                content_frame,
                text=f"{brand} {model.name}",
                bg=self.theme_config.background_color,
                font=(self.theme_config.font_family, self.theme_config.font_size + 4, 'bold')
            )
            title_label.pack(pady=(0, 15))

            # Details
            details = [
                ("Catégorie", model.category.display_name),
                ("Motorisation", model.engine_size),
                ("Carburant", model.fuel_type),
                ("Années", model.year_range),
                ("Description", model.description)
            ]

            for label, value in details:
                if value:
                    detail_frame = tk.Frame(content_frame, bg=self.theme_config.background_color)
                    detail_frame.pack(fill=tk.X, pady=2)

                    tk.Label(
                        detail_frame,
                        text=f"{label}:",
                        bg=self.theme_config.background_color,
                        font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')
                    ).pack(side=tk.LEFT)

                    tk.Label(
                        detail_frame,
                        text=value,
                        bg=self.theme_config.background_color,
                        font=(self.theme_config.font_family, self.theme_config.font_size)
                    ).pack(side=tk.LEFT, padx=(10, 0))

            # Close button
            tk.Button(
                content_frame,
                text="Fermer",
                command=detail_window.destroy,
                font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')
            ).pack(pady=(20, 0))

    def get_selection(self) -> Dict[str, str]:
        """Get current selection as dictionary"""
        return {
            'vehicle_type': self.vehicle_type,
            'category': self.selected_category.get(),
            'brand': self.selected_brand.get(),
            'model': self.selected_model.get(),
            'full_name': f"{self.selected_brand.get()} {self.selected_model.get()}".strip()
        }

    def set_selection(self, brand: str = "", model: str = "", category: str = ""):
        """Set selection programmatically"""
        if category:
            self.selected_category.set(category)
        if brand:
            self.selected_brand.set(brand)
        if model:
            self.selected_model.set(model)

    def _add_custom_category(self):
        """Add a new custom category"""
        # Create add category dialog
        dialog = tk.Toplevel(self)
        dialog.title("➕ Ajouter une Nouvelle Catégorie")
        dialog.geometry("450x500")  # Increased height for better icon display
        dialog.configure(bg=self.theme_config.background_color)
        dialog.resizable(False, False)

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f'450x500+{x}+{y}')

        # Make dialog modal
        dialog.transient(self)
        dialog.grab_set()

        # Title
        title_frame = tk.Frame(dialog, bg=self.theme_config.background_color)
        title_frame.pack(fill='x', pady=15)

        tk.Label(title_frame, text="➕ Ajouter une Catégorie Personnalisée",
                bg=self.theme_config.background_color,
                font=(self.theme_config.font_family, 14, 'bold')).pack()

        # Form container with scrollable content
        canvas = tk.Canvas(dialog, bg=self.theme_config.background_color)
        scrollbar = ttk.Scrollbar(dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.theme_config.background_color)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Form container
        form_frame = tk.Frame(scrollable_frame, bg=self.theme_config.background_color)
        form_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Category name (Internal)
        tk.Label(form_frame, text="Nom de la catégorie (interne):",
                bg=self.theme_config.background_color,
                font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')).pack(anchor='w', pady=(0, 5))

        category_name_var = tk.StringVar()
        category_name_entry = tk.Entry(form_frame, textvariable=category_name_var,
                                     width=40, font=(self.theme_config.font_family, self.theme_config.font_size))
        category_name_entry.pack(fill='x', pady=(0, 10))

        # Display name (French)
        tk.Label(form_frame, text="Nom d'affichage:",
                bg=self.theme_config.background_color,
                font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')).pack(anchor='w', pady=(0, 5))

        display_name_var = tk.StringVar()
        display_name_entry = tk.Entry(form_frame, textvariable=display_name_var,
                                    width=40, font=(self.theme_config.font_family, self.theme_config.font_size))
        display_name_entry.pack(fill='x', pady=(0, 10))

        # Vehicle type
        tk.Label(form_frame, text="Type de véhicule:",
                bg=self.theme_config.background_color,
                font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')).pack(anchor='w', pady=(0, 5))

        vehicle_type_var = tk.StringVar(value=self.vehicle_type)
        vehicle_type_combo = ttk.Combobox(form_frame, textvariable=vehicle_type_var,
                                        values=['voiture', 'moto'], state='readonly',
                                        font=(self.theme_config.font_family, self.theme_config.font_size))
        vehicle_type_combo.pack(fill='x', pady=(0, 10))

        # Icon selection
        tk.Label(form_frame, text="Icône de la catégorie:",
                bg=self.theme_config.background_color,
                font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')).pack(anchor='w', pady=(0, 5))

        # Icon container with proper spacing
        icon_container = tk.LabelFrame(form_frame, text="Choisissez une icône",
                                     bg=self.theme_config.background_color,
                                     font=(self.theme_config.font_family, self.theme_config.font_size))
        icon_container.pack(fill='x', pady=(0, 10), padx=5)

        icon_var = tk.StringVar(value="🚗")

        # Icon options with better layout
        icons = ["🚗", "🏍️", "🚛", "🚐", "🚙", "🏎️", "🚕", "🚌", "🚒", "🚑", "🚓", "🛻"]

        # Create icon grid with proper spacing
        icon_grid = tk.Frame(icon_container, bg=self.theme_config.background_color)
        icon_grid.pack(padx=10, pady=10)

        for i, icon in enumerate(icons):
            row = i // 4  # 4 icons per row for better display
            col = i % 4

            # Create frame for each icon button
            icon_btn_frame = tk.Frame(icon_grid, bg=self.theme_config.background_color,
                                    relief='solid', bd=1, width=60, height=50)
            icon_btn_frame.grid(row=row, column=col, padx=3, pady=3)
            icon_btn_frame.pack_propagate(False)  # Maintain fixed size

            icon_btn = tk.Radiobutton(icon_btn_frame, text=icon, variable=icon_var, value=icon,
                                    bg=self.theme_config.background_color,
                                    font=(self.theme_config.font_family, 20),  # Larger font for better visibility
                                    indicatoron=False, width=3, height=2)
            icon_btn.pack(expand=True, fill='both')

        # Description
        tk.Label(form_frame, text="Description (optionnel):",
                bg=self.theme_config.background_color,
                font=(self.theme_config.font_family, self.theme_config.font_size, 'bold')).pack(anchor='w', pady=(10, 5))

        description_var = tk.StringVar()
        description_entry = tk.Entry(form_frame, textvariable=description_var,
                                   width=40, font=(self.theme_config.font_family, self.theme_config.font_size))
        description_entry.pack(fill='x', pady=(0, 15))

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True, padx=(20, 0), pady=20)
        scrollbar.pack(side="right", fill="y", pady=20)

        # Buttons
        button_frame = tk.Frame(dialog, bg=self.theme_config.background_color)
        button_frame.pack(fill='x', pady=15)

        def save_category():
            category_name = category_name_var.get().strip()
            display_name = display_name_var.get().strip()
            vehicle_type = vehicle_type_var.get()
            icon = icon_var.get()
            description = description_var.get().strip()

            if not category_name or not display_name:
                tk.messagebox.showerror("Erreur", "Veuillez remplir le nom de la catégorie et le nom d'affichage")
                return

            # Add the custom category
            success = vehicle_catalog.add_custom_category(
                category_name=category_name,
                display_name=display_name,
                vehicle_type=vehicle_type,
                description=description,
                icon=icon
            )

            if success:
                tk.messagebox.showinfo("Succès", f"La catégorie '{display_name}' a été ajoutée avec succès!")

                # Refresh the category dropdown
                self._populate_initial_data()

                # Select the new category
                self.selected_category.set(display_name)

                dialog.destroy()
            else:
                tk.messagebox.showerror("Erreur", "Échec de l'ajout de la catégorie")

        tk.Button(button_frame, text="💾 Enregistrer", command=save_category,
                 font=(self.theme_config.font_family, 12, 'bold'),
                 bg='lightgreen', width=12).pack(side='left', padx=20)

        tk.Button(button_frame, text="❌ Annuler", command=dialog.destroy,
                 font=(self.theme_config.font_family, 12, 'bold'),
                 bg='lightcoral', width=12).pack(side='right', padx=20)

        # Focus on first entry
        category_name_entry.focus_set()

        # Bind mousewheel to canvas for scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
