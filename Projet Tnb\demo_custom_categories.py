#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo: Custom Categories Feature
Demonstrates the ability to add custom vehicle categories using the + button
"""

import tkinter as tk
from tkinter import ttk
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent))

from components.vehicle_selector import VehicleSelector
from models.vehicle_catalog import vehicle_catalog


class CustomCategoriesDemo:
    """Demo application for custom categories feature"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚗 Demo: Catégories Personnalisées - Custom Categories")
        self.root.geometry("1000x700")
        self.root.configure(bg='#FFFF00')

        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f'1000x700+{x}+{y}')

        self.create_ui()

    def create_ui(self):
        """Create the demo interface"""
        # Title
        title_frame = tk.Frame(self.root, bg='#FFFF00')
        title_frame.pack(fill='x', pady=20)

        tk.Label(
            title_frame,
            text="🚗 Demo: Catégories Personnalisées pour Véhicules",
            bg='#FFFF00',
            font=('Arial', 18, 'bold')
        ).pack()

        tk.Label(
            title_frame,
            text="Utilisez le bouton ➕ pour ajouter de nouvelles catégories non présentes dans la liste",
            bg='#FFFF00',
            font=('Arial', 12)
        ).pack(pady=(5, 0))

        # Instructions
        instructions_frame = tk.LabelFrame(
            self.root,
            text="📋 Instructions d'utilisation",
            bg='#FFFF00',
            font=('Arial', 12, 'bold')
        )
        instructions_frame.pack(fill='x', padx=20, pady=10)

        instructions_text = """
🔹 Cliquez sur le bouton ➕ à côté de la liste des catégories pour ajouter une nouvelle catégorie
🔹 Remplissez les informations requises: nom de catégorie, nom d'affichage, type de véhicule, icône
🔹 Choisissez une icône appropriée parmi les options disponibles
🔹 Cliquez sur "Enregistrer" pour ajouter la nouvelle catégorie
🔹 La nouvelle catégorie apparaîtra immédiatement dans la liste déroulante

Click the ➕ button next to the categories dropdown to add a new category
Fill in the required information and choose an appropriate icon
The new category will appear in the dropdown immediately after saving
        """

        tk.Label(
            instructions_frame,
            text=instructions_text.strip(),
            bg='#FFFF00',
            font=('Arial', 10),
            justify='left'
        ).pack(anchor='w', padx=15, pady=10)

        # Create notebook for different vehicle types
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=20, pady=20)

        # Car selector tab
        self.create_car_tab()

        # Motorcycle selector tab
        self.create_motorcycle_tab()

        # Custom categories management tab
        self.create_management_tab()

        # Selection display
        self.create_selection_display()

    def create_car_tab(self):
        """Create car selector tab"""
        car_frame = tk.Frame(self.notebook, bg='#FFFF00')
        self.notebook.add(car_frame, text='🚗 Voitures - Cars')

        # Info label
        info_label = tk.Label(
            car_frame,
            text="Choisissez une catégorie de voiture ou ajoutez une nouvelle catégorie avec le bouton ➕",
            bg='#FFFF00',
            font=('Arial', 11, 'italic')
        )
        info_label.pack(pady=(10, 0))

        # Car selector
        self.car_selector = VehicleSelector(
            car_frame,
            vehicle_type="voiture",
            on_selection_change=self.on_car_selection_change,
            theme_config=self.get_theme_config()
        )
        self.car_selector.pack(fill='both', expand=True, padx=10, pady=10)

    def create_motorcycle_tab(self):
        """Create motorcycle selector tab"""
        moto_frame = tk.Frame(self.notebook, bg='#FFFF00')
        self.notebook.add(moto_frame, text='🏍️ Motos - Motorcycles')

        # Info label
        info_label = tk.Label(
            moto_frame,
            text="Choisissez une catégorie de moto ou ajoutez une nouvelle catégorie avec le bouton ➕",
            bg='#FFFF00',
            font=('Arial', 11, 'italic')
        )
        info_label.pack(pady=(10, 0))

        # Motorcycle selector
        self.moto_selector = VehicleSelector(
            moto_frame,
            vehicle_type="moto",
            on_selection_change=self.on_moto_selection_change,
            theme_config=self.get_theme_config()
        )
        self.moto_selector.pack(fill='both', expand=True, padx=10, pady=10)

    def create_management_tab(self):
        """Create custom categories management tab"""
        mgmt_frame = tk.Frame(self.notebook, bg='#FFFF00')
        self.notebook.add(mgmt_frame, text='⚙️ Gestion des Catégories - Manage Categories')

        # Title
        tk.Label(
            mgmt_frame,
            text="Gestion des Catégories Personnalisées",
            bg='#FFFF00',
            font=('Arial', 14, 'bold')
        ).pack(pady=20)

        # Custom categories list
        list_frame = tk.LabelFrame(
            mgmt_frame,
            text="Catégories Personnalisées Actuelles",
            bg='#FFFF00',
            font=('Arial', 12, 'bold')
        )
        list_frame.pack(fill='both', expand=True, padx=40, pady=20)

        # Create treeview for custom categories
        columns = ('Nom Catégorie', 'Nom Affichage', 'Type Véhicule', 'Icône', 'Description')
        self.categories_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        # Configure columns
        for col in columns:
            self.categories_tree.heading(col, text=col)
            self.categories_tree.column(col, width=150)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        self.categories_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # Refresh button
        refresh_btn = tk.Button(
            mgmt_frame,
            text="🔄 Actualiser la Liste",
            command=self.refresh_categories_list,
            font=('Arial', 12, 'bold'),
            bg='lightblue'
        )
        refresh_btn.pack(pady=10)

        # Load initial data
        self.refresh_categories_list()

    def create_selection_display(self):
        """Create selection display at bottom"""
        display_frame = tk.Frame(self.root, bg='#FFFF00', relief='solid', bd=2)
        display_frame.pack(fill='x', padx=20, pady=(0, 20))

        tk.Label(
            display_frame,
            text="Sélection Actuelle - Current Selection:",
            bg='#FFFF00',
            font=('Arial', 12, 'bold')
        ).pack(side='left', padx=15, pady=10)

        self.selection_label = tk.Label(
            display_frame,
            text="Aucune sélection - No selection",
            bg='white',
            font=('Arial', 12),
            relief='solid',
            bd=1,
            padx=15,
            pady=5
        )
        self.selection_label.pack(side='left', padx=(0, 15), pady=10)

        # Demo buttons
        demo_frame = tk.Frame(display_frame, bg='#FFFF00')
        demo_frame.pack(side='right', padx=15, pady=10)

        tk.Button(
            demo_frame,
            text="➕ Ajouter Catégorie Voiture",
            command=lambda: self.add_demo_category('voiture'),
            font=('Arial', 10, 'bold'),
            bg='lightgreen'
        ).pack(side='left', padx=5)

        tk.Button(
            demo_frame,
            text="➕ Ajouter Catégorie Moto",
            command=lambda: self.add_demo_category('moto'),
            font=('Arial', 10, 'bold'),
            bg='lightcoral'
        ).pack(side='left', padx=5)

    def get_theme_config(self):
        """Get theme configuration"""
        class ThemeConfig:
            background_color = '#FFFF00'
            font_family = 'Arial'
            font_size = 10
        return ThemeConfig()

    def on_car_selection_change(self, selection):
        """Handle car selection change"""
        if selection['brand'] and selection['model']:
            text = f"🚗 {selection['brand']} {selection['model']} ({selection['category']})"
        else:
            text = f"🚗 {selection['vehicle_type'].title()} - {selection['category']}"

        self.selection_label.config(text=text)

    def on_moto_selection_change(self, selection):
        """Handle motorcycle selection change"""
        if selection['brand'] and selection['model']:
            text = f"🏍️ {selection['brand']} {selection['model']} ({selection['category']})"
        else:
            text = f"🏍️ {selection['vehicle_type'].title()} - {selection['category']}"

        self.selection_label.config(text=text)

    def refresh_categories_list(self):
        """Refresh the custom categories list"""
        # Clear existing items
        for item in self.categories_tree.get_children():
            self.categories_tree.delete(item)

        # Add custom categories
        for category_key, category_info in vehicle_catalog._custom_categories.items():
            self.categories_tree.insert('', 'end', values=(
                category_key,
                category_info.get('display_name', ''),
                category_info.get('vehicle_type', ''),
                category_info.get('icon', ''),
                category_info.get('description', '')
            ))

    def add_demo_category(self, vehicle_type):
        """Add a demo category"""
        if vehicle_type == 'voiture':
            success = vehicle_catalog.add_custom_category(
                category_name="voiture_electrique",
                display_name="Voiture Électrique",
                vehicle_type="voiture",
                description="Véhicules fonctionnant à l'électricité",
                icon="⚡"
            )
            if success:
                tk.messagebox.showinfo("Succès", "La catégorie 'Voiture Électrique' a été ajoutée comme exemple!")
        else:
            success = vehicle_catalog.add_custom_category(
                category_name="moto_electrique",
                display_name="Moto Électrique",
                vehicle_type="moto",
                description="Motos fonctionnant à l'électricité",
                icon="⚡"
            )
            if success:
                tk.messagebox.showinfo("Succès", "La catégorie 'Moto Électrique' a été ajoutée comme exemple!")

        # Refresh both selectors and management list
        self.car_selector._populate_initial_data()
        self.moto_selector._populate_initial_data()
        self.refresh_categories_list()

    def run(self):
        """Run the demo"""
        self.root.mainloop()


def main():
    """Main function"""
    try:
        demo = CustomCategoriesDemo()
        demo.run()
    except Exception as e:
        print(f"Error running demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
