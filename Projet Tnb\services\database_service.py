#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Service - Professional Data Persistence Layer
Supports multiple backends: JSON, SQLite, PostgreSQL
"""

import json
import sqlite3
import os
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from models.vehicle import Vehicle, VehicleCollection


class DatabaseInterface(ABC):
    """Abstract interface for database operations"""
    
    @abstractmethod
    def save_vehicles(self, vehicles: VehicleCollection) -> bool:
        """Save vehicle collection"""
        pass
    
    @abstractmethod
    def load_vehicles(self) -> VehicleCollection:
        """Load vehicle collection"""
        pass
    
    @abstractmethod
    def backup(self, backup_path: str) -> bool:
        """Create backup"""
        pass
    
    @abstractmethod
    def restore(self, backup_path: str) -> bool:
        """Restore from backup"""
        pass


class JSONDatabaseService(DatabaseInterface):
    """JSON file-based database service (current implementation)"""
    
    def __init__(self, file_path: str = "vehicle_data.json"):
        self.file_path = file_path
        self.backup_dir = "backups"
        Path(self.backup_dir).mkdir(exist_ok=True)
    
    def save_vehicles(self, vehicles: VehicleCollection) -> bool:
        """Save vehicles to JSON file"""
        try:
            # Create backup before saving
            self._create_auto_backup()
            
            data = vehicles.to_dict_list()
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"Error saving to JSON: {e}")
            return False
    
    def load_vehicles(self) -> VehicleCollection:
        """Load vehicles from JSON file"""
        try:
            if not os.path.exists(self.file_path):
                return VehicleCollection()
            
            with open(self.file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return VehicleCollection.from_dict_list(data)
        except Exception as e:
            print(f"Error loading from JSON: {e}")
            return VehicleCollection()
    
    def backup(self, backup_path: str) -> bool:
        """Create manual backup"""
        try:
            if os.path.exists(self.file_path):
                import shutil
                shutil.copy2(self.file_path, backup_path)
                return True
            return False
        except Exception as e:
            print(f"Error creating backup: {e}")
            return False
    
    def restore(self, backup_path: str) -> bool:
        """Restore from backup"""
        try:
            if os.path.exists(backup_path):
                import shutil
                shutil.copy2(backup_path, self.file_path)
                return True
            return False
        except Exception as e:
            print(f"Error restoring backup: {e}")
            return False
    
    def _create_auto_backup(self) -> None:
        """Create automatic backup with timestamp"""
        if os.path.exists(self.file_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"vehicle_data_backup_{timestamp}.json"
            backup_path = os.path.join(self.backup_dir, backup_name)
            self.backup(backup_path)
            
            # Keep only last 10 backups
            self._cleanup_old_backups()
    
    def _cleanup_old_backups(self) -> None:
        """Keep only the 10 most recent backups"""
        try:
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.startswith("vehicle_data_backup_") and file.endswith(".json"):
                    file_path = os.path.join(self.backup_dir, file)
                    backup_files.append((file_path, os.path.getctime(file_path)))
            
            # Sort by creation time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # Remove old backups (keep only 10)
            for file_path, _ in backup_files[10:]:
                os.remove(file_path)
        except Exception as e:
            print(f"Error cleaning up backups: {e}")


class SQLiteDatabaseService(DatabaseInterface):
    """SQLite database service for better performance and features"""
    
    def __init__(self, db_path: str = "data/vehicles.db"):
        self.db_path = db_path
        Path(os.path.dirname(db_path)).mkdir(parents=True, exist_ok=True)
        self._initialize_database()
    
    def _initialize_database(self) -> None:
        """Initialize database schema"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS vehicles (
                    id TEXT PRIMARY KEY,
                    nom_prenom TEXT NOT NULL,
                    vehicule TEXT NOT NULL,
                    vehicle_type TEXT NOT NULL,
                    date_mise_fourriere DATE,
                    date_retrait DATE,
                    taux REAL NOT NULL DEFAULT 20.0,
                    numero_quittance TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT DEFAULT 'system'
                )
            """)
            
            # Create indexes for better performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_nom_prenom ON vehicles(nom_prenom)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_vehicule ON vehicles(vehicule)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_created_date ON vehicles(created_date)")
            
            conn.commit()
    
    def save_vehicles(self, vehicles: VehicleCollection) -> bool:
        """Save vehicles to SQLite database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Clear existing data (for full sync)
                conn.execute("DELETE FROM vehicles")
                
                # Insert all vehicles
                for vehicle in vehicles:
                    self._insert_vehicle(conn, vehicle)
                
                conn.commit()
            return True
        except Exception as e:
            print(f"Error saving to SQLite: {e}")
            return False
    
    def load_vehicles(self) -> VehicleCollection:
        """Load vehicles from SQLite database"""
        try:
            collection = VehicleCollection()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row  # Enable column access by name
                cursor = conn.execute("""
                    SELECT * FROM vehicles 
                    ORDER BY created_date DESC
                """)
                
                for row in cursor:
                    vehicle_data = dict(row)
                    vehicle = Vehicle.from_dict(vehicle_data)
                    collection.vehicles.append(vehicle)
            
            return collection
        except Exception as e:
            print(f"Error loading from SQLite: {e}")
            return VehicleCollection()
    
    def _insert_vehicle(self, conn: sqlite3.Connection, vehicle: Vehicle) -> None:
        """Insert single vehicle into database"""
        conn.execute("""
            INSERT OR REPLACE INTO vehicles (
                id, nom_prenom, vehicule, vehicle_type,
                date_mise_fourriere, date_retrait, taux, numero_quittance,
                created_date, modified_date, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            vehicle.id,
            vehicle.nom_prenom,
            vehicle.vehicule,
            vehicle.vehicle_type.value,
            vehicle.date_mise_fourriere.isoformat() if vehicle.date_mise_fourriere else None,
            vehicle.date_retrait.isoformat() if vehicle.date_retrait else None,
            vehicle.taux,
            vehicle.numero_quittance,
            vehicle.created_date.isoformat(),
            vehicle.modified_date.isoformat(),
            vehicle.created_by
        ))
    
    def backup(self, backup_path: str) -> bool:
        """Create database backup"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            print(f"Error creating SQLite backup: {e}")
            return False
    
    def restore(self, backup_path: str) -> bool:
        """Restore database from backup"""
        try:
            import shutil
            shutil.copy2(backup_path, self.db_path)
            return True
        except Exception as e:
            print(f"Error restoring SQLite backup: {e}")
            return False
    
    def search_vehicles(self, query: str) -> VehicleCollection:
        """Advanced search with SQL"""
        try:
            collection = VehicleCollection()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM vehicles 
                    WHERE nom_prenom LIKE ? OR vehicule LIKE ?
                    ORDER BY created_date DESC
                """, (f"%{query}%", f"%{query}%"))
                
                for row in cursor:
                    vehicle_data = dict(row)
                    vehicle = Vehicle.from_dict(vehicle_data)
                    collection.vehicles.append(vehicle)
            
            return collection
        except Exception as e:
            print(f"Error searching SQLite: {e}")
            return VehicleCollection()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_vehicles,
                        SUM(taux * (julianday(date_retrait) - julianday(date_mise_fourriere))) as total_revenue,
                        AVG(taux * (julianday(date_retrait) - julianday(date_mise_fourriere))) as avg_revenue,
                        vehicle_type,
                        COUNT(*) as type_count
                    FROM vehicles 
                    WHERE date_mise_fourriere IS NOT NULL AND date_retrait IS NOT NULL
                    GROUP BY vehicle_type
                """)
                
                stats = {
                    'total_vehicles': 0,
                    'total_revenue': 0.0,
                    'avg_revenue': 0.0,
                    'by_type': {}
                }
                
                for row in cursor:
                    stats['total_vehicles'] += row[4]
                    if row[1]:  # total_revenue
                        stats['total_revenue'] += row[1]
                    stats['by_type'][row[3]] = row[4]
                
                if stats['total_vehicles'] > 0:
                    stats['avg_revenue'] = stats['total_revenue'] / stats['total_vehicles']
                
                return stats
        except Exception as e:
            print(f"Error getting statistics: {e}")
            return {'total_vehicles': 0, 'total_revenue': 0.0, 'avg_revenue': 0.0, 'by_type': {}}


class DatabaseFactory:
    """Factory for creating database services"""
    
    @staticmethod
    def create_database(db_type: str = "json", **kwargs) -> DatabaseInterface:
        """Create database service based on type"""
        if db_type.lower() == "json":
            return JSONDatabaseService(kwargs.get('file_path', 'vehicle_data.json'))
        elif db_type.lower() == "sqlite":
            return SQLiteDatabaseService(kwargs.get('db_path', 'data/vehicles.db'))
        else:
            raise ValueError(f"Unsupported database type: {db_type}")


# Migration utility
class DatabaseMigrator:
    """Utility for migrating between database types"""
    
    @staticmethod
    def migrate_json_to_sqlite(json_path: str = "vehicle_data.json", 
                              sqlite_path: str = "data/vehicles.db") -> bool:
        """Migrate from JSON to SQLite"""
        try:
            # Load from JSON
            json_db = JSONDatabaseService(json_path)
            vehicles = json_db.load_vehicles()
            
            # Save to SQLite
            sqlite_db = SQLiteDatabaseService(sqlite_path)
            return sqlite_db.save_vehicles(vehicles)
        except Exception as e:
            print(f"Migration error: {e}")
            return False
    
    @staticmethod
    def migrate_sqlite_to_json(sqlite_path: str = "data/vehicles.db",
                              json_path: str = "vehicle_data_export.json") -> bool:
        """Migrate from SQLite to JSON"""
        try:
            # Load from SQLite
            sqlite_db = SQLiteDatabaseService(sqlite_path)
            vehicles = sqlite_db.load_vehicles()
            
            # Save to JSON
            json_db = JSONDatabaseService(json_path)
            return json_db.save_vehicles(vehicles)
        except Exception as e:
            print(f"Migration error: {e}")
            return False
