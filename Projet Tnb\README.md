# Vehicle Management Interface

A French vehicle management system with a bright yellow background, built using Python and tkinter.

## Features

- **Bright yellow background** matching the original design
- **Top button bar** with actions: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Actualiser
- **Vehicle information section** for entering:
  - Name and surname
  - Vehicle name or registration
  - Impound date (Date de mise en fourrière)
  - Withdrawal date (Date de retrait)
- **Vehicle type selection grid** with icons and checkboxes for:
  - 🚲 Bicyclette (Bicycle)
  - 🏍️ Moto (Motorcycle)
  - 🚗 Voiture (Car) - selected by default
  - 🚐 Camionnette (Van)
  - 🚛 Camion (Truck)
  - 🚚 Grand camion (Large truck)
  - ♻️ Matériel ammort (Depreciated equipment)
  - 📦 Matériel non ammort (Non-depreciated equipment)
- **Financial summary section** showing:
  - Total number of days
  - Rate (Taux)
  - Amount to pay (Montant à payer)
  - Receipt number (Numéro de la quittance)
- **Navigation controls** (previous/next record)
- **Action buttons** for printing receipts and showing lists
- **Bottom total display** showing the grand total

## Requirements

- Python 3.x (with tkinter - included in standard Python installation)
- No additional packages required

## Installation

1. <PERSON>lone or download this repository
2. Navigate to the project directory:
   ```bash
   cd "Projet Tnb"
   ```

## Usage

Run the application:
```bash
python vehicle_management.py
```

### Interface Elements

1. **Top Buttons**: Click any button to see action confirmations
2. **Vehicle Information**: Edit the text fields to modify vehicle data
3. **Vehicle Type Grid**: Check/uncheck boxes to select vehicle types
4. **Financial Data**: View and modify financial information
5. **Navigation**: Use ◀ and ▶ buttons to navigate between records
6. **Print Options**: 
   - "imprimer bon" - Print single receipt
   - "imprimer bon 3 COPIES" - Print 3 copies
   - "LISTE" - Show list view

## File Structure

```
Projet Tnb/
├── vehicle_management.py  # Main application file
├── requirements.txt       # Dependencies (none required)
└── README.md             # This documentation
```

## Customization

The application can be easily customized by modifying:

- **Colors**: Change the `bg='#FFFF00'` parameter to use different background colors
- **Vehicle data**: Modify the `vehicle_data` dictionary in the `__init__` method
- **Vehicle types**: Add or remove items from the `vehicle_info` list
- **Language**: All text strings can be modified for different languages

## Technical Details

- Built with Python's tkinter GUI framework
- Uses emoji icons for vehicle types
- Responsive grid layout
- Event-driven architecture with message boxes for user feedback

## License

This project is open source and available under the MIT License.
