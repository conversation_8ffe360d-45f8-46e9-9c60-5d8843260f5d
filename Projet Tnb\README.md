# Vehicle Management Interface

A French vehicle management system with a bright yellow background, built using Python and tkinter.

## Features

- **Bright yellow background** matching the original design
- **Top button bar** with actions: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Actualiser
- **Vehicle information section** for entering:
  - Name and surname
  - Vehicle name or registration
  - Impound date (Date de mise en fourrière)
  - Withdrawal date (Date de retrait)
- **Vehicle type selection grid** with icons and checkboxes for:
  - 🚲 Bicyclette (Bicycle)
  - 🏍️ Moto (Motorcycle)
  - 🚗 Voiture (Car) - selected by default
  - 🚐 Camionnette (Van)
  - 🚛 Camion (Truck)
  - 🚚 Grand camion (Large truck)
  - ♻️ Matériel ammort (Depreciated equipment)
  - 📦 Matériel non ammort (Non-depreciated equipment)
- **Financial summary section** showing:
  - Total number of days
  - Rate (Taux)
  - Amount to pay (Montant à payer)
  - Receipt number (Numéro de la quittance)
- **Navigation controls** (previous/next record)
- **Action buttons** for printing receipts and showing lists
- **Bottom total display** showing the grand total

## Requirements

- Python 3.x (with tkinter - included in standard Python installation)
- **Screen Resolution**: Optimized for 900x700 (works on all devices)
- No additional packages required

## Installation

1. Clone or download this repository
2. Navigate to the project directory:
   ```bash
   cd "Projet Tnb"
   ```

## Usage

Run the application:
```bash
python vehicle_management.py
```

### Interface Elements

1. **Top Buttons** (Fully Functional):
   - **Ajouter**: Add a new vehicle record
   - **Sauvegarder**: Save current record and all data to file
   - **Rechercher**: Search records by name or vehicle
   - **Supprimer**: Delete current record with confirmation
   - **Actualiser**: Refresh data from file

2. **Vehicle Information**:
   - Edit text fields to modify vehicle data
   - **Auto-calculation**: Days are calculated automatically from dates
   - **Record Counter**: Shows current record position (e.g., "2/5")

3. **Advanced Vehicle Selector**:
   - **Tabbed Interface**: Separate tabs for Cars (🚗), Motorcycles (🏍️), and Other Vehicles (🚛)
   - **Detailed Car Database**: 50+ models from 10+ brands (Peugeot, Renault, Toyota, BMW, etc.)
   - **Comprehensive Motorcycle Catalog**: 40+ models from 6+ brands (Yamaha, Honda, Kawasaki, etc.)
   - **Category Filtering**: Filter by vehicle categories (Citadine, Berline, SUV, Sportive, etc.)
   - **Cascading Dropdowns**: Type → Category → Brand → Model selection
   - **Real-time Search**: Search vehicles by name or brand
   - **Detailed Information**: Engine size, fuel type, year range, descriptions
   - **Professional UI**: Attractive dropdowns consistent with program style

4. **Financial Data**:
   - **Auto-calculation**: Amount is calculated from days × rate
   - **Real-time Updates**: Changes are reflected immediately
   - **Validation**: Numeric fields are validated

5. **Navigation**:
   - **◀ ▶ buttons**: Navigate between records
   - **Auto-save**: Modified data is saved when navigating
   - **Smart Navigation**: Prevents data loss

6. **Print Options**:
   - **"imprimer bon"**: Print receipt with full details in preview window
   - **"imprimer bon 3 COPIES"**: Print 3 copies (simulated)
   - **"LISTE"**: Show sortable list of all records in table format

7. **Data Management**:
   - **JSON Storage**: All data saved to `vehicle_data.json`
   - **Auto-backup**: Data is preserved between sessions
   - **Multi-record Support**: Handle unlimited vehicle records

## File Structure

```
Projet Tnb/
├── vehicle_management.py  # Main application file (690+ lines)
├── vehicle_data.json     # Data storage file (auto-created)
├── requirements.txt      # Dependencies (none required)
└── README.md            # This documentation
```

## New Enhanced Features

### 🔧 **Fully Functional Buttons**
All top buttons now perform real actions:
- **Add**: Creates new records with auto-generated receipt numbers
- **Save**: Persists data to JSON file with error handling
- **Search**: Find records by name or vehicle with live results
- **Delete**: Remove records with confirmation dialogs
- **Refresh**: Reload data from file with unsaved changes protection

### 🧮 **Smart Calculations**
- **Auto-date calculation**: Days between dates calculated automatically
- **Dynamic pricing**: Amount = Days × Rate (updates in real-time)
- **Running totals**: Grand total updates across all records
- **Input validation**: Prevents invalid data entry

### 🚗 **Enhanced Vehicle Management**
- **Single-select vehicle types**: Only one type can be selected
- **Visual feedback**: Selected type displayed prominently
- **Type coordination**: Vehicle type syncs with record data

### 📊 **Advanced Data Features**
- **Multi-record support**: Handle unlimited vehicle records
- **JSON persistence**: Data saved between sessions
- **Record navigation**: Browse through records with auto-save
- **Search functionality**: Find records quickly
- **Data integrity**: Validation and error handling

### 🖨️ **Professional Printing**
- **Receipt preview**: Compact print preview window (500x600)
- **Formatted output**: Professional receipt layout
- **Multiple copies**: Support for printing multiple copies
- **List view**: Efficient tabular display (900x500) with sorting

### 🖥️ **Universal Compact Design (900x700)**
- **Optimized buttons**: 14-character wide buttons with clear fonts
- **Balanced input fields**: 45-character wide text entries
- **Advanced vehicle selector**: Professional tabbed interface with detailed filtering
- **Clear totals**: 16pt bold total display
- **Centered windows**: All dialogs automatically centered on screen
- **Universal compatibility**: Works on laptops, desktops, and tablets
- **Efficient spacing**: Maximizes content while maintaining readability

### 🚗 **Advanced Vehicle Database**
- **Comprehensive Car Catalog**: 50+ models from major brands
  - **French**: Peugeot (208, 308, 508, 2008, 3008), Renault (Clio, Mégane, Captur), Citroën (C3, C4, C5 Aircross)
  - **German**: BMW (Série 1, 3, 5, X1, X3), Mercedes (Classe A, C, E, GLA, GLC), Volkswagen (Polo, Golf, Tiguan)
  - **Japanese**: Toyota (Yaris, Corolla, RAV4), Honda (Jazz, Civic, CR-V)
  - **Korean**: Hyundai (i10, i20, Tucson), Kia (Picanto, Sportage, Sorento)

- **Detailed Motorcycle Catalog**: 40+ models from top brands
  - **Japanese**: Yamaha (R1, R6, MT-09, MT-07), Honda (CBR1000RR, CB650R, Africa Twin), Kawasaki (Ninja, Z900, Versys), Suzuki (GSX-R1000, V-Strom)
  - **European**: Ducati (Panigale V4, Monster, Multistrada), BMW (S1000RR, R1250GS, F850GS)

- **Smart Categorization**:
  - **Cars**: Citadine, Berline, SUV, Coupé, Break, Monospace, Utilitaire
  - **Motorcycles**: Sportive, Roadster, Trail, Touring, Custom, Scooter, Cross

### 🔍 **Advanced Filtering System**
- **Category-based filtering**: Filter vehicles by specific categories
- **Cascading selection**: Type → Category → Brand → Model
- **Real-time search**: Find vehicles instantly by typing
- **Detailed specifications**: Engine size, fuel type, year range, descriptions
- **Professional dropdowns**: Consistent styling with main application

## Customization

The application can be easily customized by modifying:

- **Colors**: Change the `bg='#FFFF00'` parameter to use different background colors
- **Vehicle data**: Modify the `vehicle_data` dictionary in the `__init__` method
- **Vehicle types**: Add or remove items from the `vehicle_info` list
- **Language**: All text strings can be modified for different languages

## Technical Details

- Built with Python's tkinter GUI framework
- Uses emoji icons for vehicle types
- Responsive grid layout
- Event-driven architecture with message boxes for user feedback

## License

This project is open source and available under the MIT License.
