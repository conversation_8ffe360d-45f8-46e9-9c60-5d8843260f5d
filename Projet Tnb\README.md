# Vehicle Management Interface

A French vehicle management system with a bright yellow background, built using Python and tkinter.

## Features

- **Bright yellow background** matching the original design
- **Top button bar** with actions: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Actualiser
- **Vehicle information section** for entering:
  - Name and surname
  - Vehicle name or registration
  - Impound date (Date de mise en fourrière)
  - Withdrawal date (Date de retrait)
- **Vehicle type selection grid** with icons and checkboxes for:
  - 🚲 Bicyclette (Bicycle)
  - 🏍️ Moto (Motorcycle)
  - 🚗 Voiture (Car) - selected by default
  - 🚐 Camionnette (Van)
  - 🚛 Camion (Truck)
  - 🚚 Grand camion (Large truck)
  - ♻️ Matériel ammort (Depreciated equipment)
  - 📦 Matériel non ammort (Non-depreciated equipment)
- **Financial summary section** showing:
  - Total number of days
  - Rate (Taux)
  - Amount to pay (Montant à payer)
  - Receipt number (Numéro de la quittance)
- **Navigation controls** (previous/next record)
- **Action buttons** for printing receipts and showing lists
- **Bottom total display** showing the grand total

## Requirements

- Python 3.x (with tkinter - included in standard Python installation)
- **Screen Resolution**: Optimized for 900x700 (works on all devices)
- No additional packages required

## Installation

1. Clone or download this repository
2. Navigate to the project directory:
   ```bash
   cd "Projet Tnb"
   ```

## Usage

Run the application:
```bash
python vehicle_management.py
```

### Interface Elements

1. **Top Buttons** (Fully Functional):
   - **Ajouter**: Add a new vehicle record
   - **Sauvegarder**: Save current record and all data to file
   - **Rechercher**: Search records by name or vehicle
   - **Supprimer**: Delete current record with confirmation
   - **Actualiser**: Refresh data from file

2. **Vehicle Information**:
   - Edit text fields to modify vehicle data
   - **Auto-calculation**: Days are calculated automatically from dates
   - **Record Counter**: Shows current record position (e.g., "2/5")

3. **Vehicle Type Grid**:
   - **Single Selection**: Only one vehicle type can be selected at a time
   - **Real-time Update**: Selected type is displayed below the grid
   - **8 Vehicle Types**: Bicycle, motorcycle, car, van, truck, large truck, depreciated/non-depreciated equipment

4. **Financial Data**:
   - **Auto-calculation**: Amount is calculated from days × rate
   - **Real-time Updates**: Changes are reflected immediately
   - **Validation**: Numeric fields are validated

5. **Navigation**:
   - **◀ ▶ buttons**: Navigate between records
   - **Auto-save**: Modified data is saved when navigating
   - **Smart Navigation**: Prevents data loss

6. **Print Options**:
   - **"imprimer bon"**: Print receipt with full details in preview window
   - **"imprimer bon 3 COPIES"**: Print 3 copies (simulated)
   - **"LISTE"**: Show sortable list of all records in table format

7. **Data Management**:
   - **JSON Storage**: All data saved to `vehicle_data.json`
   - **Auto-backup**: Data is preserved between sessions
   - **Multi-record Support**: Handle unlimited vehicle records

## File Structure

```
Projet Tnb/
├── vehicle_management.py  # Main application file (690+ lines)
├── vehicle_data.json     # Data storage file (auto-created)
├── requirements.txt      # Dependencies (none required)
└── README.md            # This documentation
```

## New Enhanced Features

### 🔧 **Fully Functional Buttons**
All top buttons now perform real actions:
- **Add**: Creates new records with auto-generated receipt numbers
- **Save**: Persists data to JSON file with error handling
- **Search**: Find records by name or vehicle with live results
- **Delete**: Remove records with confirmation dialogs
- **Refresh**: Reload data from file with unsaved changes protection

### 🧮 **Smart Calculations**
- **Auto-date calculation**: Days between dates calculated automatically
- **Dynamic pricing**: Amount = Days × Rate (updates in real-time)
- **Running totals**: Grand total updates across all records
- **Input validation**: Prevents invalid data entry

### 🚗 **Enhanced Vehicle Management**
- **Single-select vehicle types**: Only one type can be selected
- **Visual feedback**: Selected type displayed prominently
- **Type coordination**: Vehicle type syncs with record data

### 📊 **Advanced Data Features**
- **Multi-record support**: Handle unlimited vehicle records
- **JSON persistence**: Data saved between sessions
- **Record navigation**: Browse through records with auto-save
- **Search functionality**: Find records quickly
- **Data integrity**: Validation and error handling

### 🖨️ **Professional Printing**
- **Receipt preview**: Compact print preview window (500x600)
- **Formatted output**: Professional receipt layout
- **Multiple copies**: Support for printing multiple copies
- **List view**: Efficient tabular display (900x500) with sorting

### 🖥️ **Universal Compact Design (900x700)**
- **Optimized buttons**: 14-character wide buttons with clear fonts
- **Balanced input fields**: 45-character wide text entries
- **Compact vehicle grid**: Efficient checkboxes with readable fonts
- **Clear totals**: 16pt bold total display
- **Centered windows**: All dialogs automatically centered on screen
- **Universal compatibility**: Works on laptops, desktops, and tablets
- **Efficient spacing**: Maximizes content while maintaining readability

## Customization

The application can be easily customized by modifying:

- **Colors**: Change the `bg='#FFFF00'` parameter to use different background colors
- **Vehicle data**: Modify the `vehicle_data` dictionary in the `__init__` method
- **Vehicle types**: Add or remove items from the `vehicle_info` list
- **Language**: All text strings can be modified for different languages

## Technical Details

- Built with Python's tkinter GUI framework
- Uses emoji icons for vehicle types
- Responsive grid layout
- Event-driven architecture with message boxes for user feedback

## License

This project is open source and available under the MIT License.
