# Vehicle Management System Configuration
# Professional configuration management

# Application Settings
app:
  name: "Gestion des Véhicules"
  version: "2.0.0"
  author: "Professional Development Team"
  debug: false
  log_level: "INFO"

# Database Configuration
database:
  # Options: json, sqlite, postgresql
  type: "json"
  
  # JSON Database Settings
  json:
    file_path: "vehicle_data.json"
    backup_enabled: true
    backup_retention_days: 30
    auto_backup_interval: 24  # hours
  
  # SQLite Database Settings
  sqlite:
    db_path: "data/vehicles.db"
    backup_enabled: true
    vacuum_on_startup: true
    wal_mode: true
  
  # PostgreSQL Database Settings (Future)
  postgresql:
    host: "localhost"
    port: 5432
    database: "vehicle_management"
    username: "vm_user"
    password: "${DB_PASSWORD}"  # Environment variable
    pool_size: 10
    ssl_mode: "prefer"

# User Interface Configuration
ui:
  # Window Settings
  window:
    width: 900
    height: 700
    resizable: true
    center_on_screen: true
    min_width: 800
    min_height: 600
  
  # Theme Settings
  theme:
    background_color: "#FFFF00"  # Bright yellow
    primary_color: "#000000"     # Black text
    secondary_color: "#808080"   # Gray
    accent_color: "#FFFFFF"      # White
    font_family: "Arial"
    font_size: 10
    icon_size: 16
  
  # Language and Localization
  localization:
    language: "fr"  # French
    currency: "DH"  # Dirham
    date_format: "%d/%m/%Y"
    number_format: "fr_FR"
    timezone: "Africa/Casablanca"

# Business Rules Configuration
business:
  # Default Values
  defaults:
    vehicle_type: "voiture"
    daily_rate: 20.00
    currency: "DH"
    receipt_prefix: "R"
  
  # Validation Rules
  validation:
    min_rate: 0.0
    max_rate: 1000.0
    max_days: 365
    required_fields: ["nom_prenom", "vehicule"]
  
  # Calculation Rules
  calculations:
    round_to_decimals: 2
    include_tax: false
    tax_rate: 0.20  # 20% VAT
    discount_enabled: false

# Security Configuration
security:
  # Authentication (Future)
  authentication:
    enabled: false
    method: "local"  # local, oauth, ldap
    session_timeout: 3600  # seconds
    password_policy:
      min_length: 8
      require_uppercase: true
      require_numbers: true
      require_special_chars: false
  
  # Data Protection
  data_protection:
    encryption_enabled: false
    backup_encryption: false
    audit_trail: true
    data_retention_days: 2555  # 7 years

# Reporting Configuration
reporting:
  # Export Formats
  export_formats: ["pdf", "excel", "csv", "json"]
  
  # Report Templates
  templates:
    receipt: "templates/receipt.html"
    list: "templates/vehicle_list.html"
    summary: "templates/summary_report.html"
  
  # PDF Settings
  pdf:
    page_size: "A4"
    orientation: "portrait"
    margin: 20
    font_size: 12
    include_logo: false
    logo_path: "assets/logo.png"

# Integration Configuration
integrations:
  # Email Settings (Future)
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "${EMAIL_USERNAME}"
    password: "${EMAIL_PASSWORD}"
    use_tls: true
  
  # SMS Settings (Future)
  sms:
    enabled: false
    provider: "twilio"
    api_key: "${SMS_API_KEY}"
    from_number: "+**********"
  
  # Payment Gateway (Future)
  payment:
    enabled: false
    provider: "stripe"
    api_key: "${PAYMENT_API_KEY}"
    webhook_secret: "${PAYMENT_WEBHOOK_SECRET}"

# Performance Configuration
performance:
  # Caching
  cache:
    enabled: true
    type: "memory"  # memory, redis
    ttl: 3600  # seconds
    max_size: 1000  # items
  
  # Database Optimization
  database_optimization:
    connection_pooling: true
    query_timeout: 30  # seconds
    batch_size: 100
    index_optimization: true

# Logging Configuration
logging:
  # Log Levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "INFO"
  
  # Log Destinations
  handlers:
    console:
      enabled: true
      level: "INFO"
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    file:
      enabled: true
      level: "DEBUG"
      file_path: "logs/vehicle_management.log"
      max_size: "10MB"
      backup_count: 5
      format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
    
    # Future: Remote logging
    remote:
      enabled: false
      endpoint: "https://logs.example.com/api/logs"
      api_key: "${LOG_API_KEY}"

# Development Configuration
development:
  # Debug Settings
  debug_mode: false
  hot_reload: false
  profiling: false
  
  # Testing
  testing:
    test_data_enabled: true
    mock_external_services: true
    test_database: "test_vehicles.db"
  
  # Development Tools
  tools:
    auto_migration: true
    schema_validation: true
    performance_monitoring: false

# Deployment Configuration
deployment:
  # Environment
  environment: "development"  # development, staging, production
  
  # Health Checks
  health_checks:
    enabled: true
    database_check: true
    disk_space_check: true
    memory_check: true
  
  # Monitoring
  monitoring:
    enabled: false
    metrics_endpoint: "/metrics"
    health_endpoint: "/health"
    
# Feature Flags
features:
  # Current Features
  multi_user: false
  advanced_search: true
  export_functionality: true
  backup_restore: true
  
  # Future Features
  web_interface: false
  mobile_app: false
  api_access: false
  real_time_sync: false
  analytics_dashboard: false
  payment_processing: false
